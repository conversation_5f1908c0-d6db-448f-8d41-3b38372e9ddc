# 豆瓣《绿皮书》电影评论多模态文本分析

## 项目概述

本项目对豆瓣《绿皮书》电影评论进行全面的多模态文本分析，包括情感分析、词频统计、机器学习建模、文本聚类、主题模型和社交网络分析。

## 功能特性

### 1. 数据预处理
- ✅ 文本清洗：去除标点、特殊符号、停用词
- ✅ 自定义词典：集成电影专有名词
- ✅ 分词工具：jieba精确模式分词

### 2. 情感倾向分析
- ✅ 基于SnowNLP的情感评分（0-1连续值）
- ✅ 情感分布直方图
- ✅ 正/负/中性评论占比饼图

### 3. 高频词与词云分析
- ✅ TF-IDF权重分析（Top 50词汇）
- ✅ 词云图生成
- ✅ 特殊关键词频次统计

### 4. 机器学习预测建模
- ✅ 决策树分类器（max_depth=5, criterion='entropy'）
- ✅ 朴素贝叶斯分类器（alpha=1.0）
- ✅ 支持向量机（kernel='linear', C=1）
- ✅ 模型性能评估：准确率、F1-score、混淆矩阵

### 5. 文本聚类与LDA主题模型
- ✅ K-means聚类（n_clusters=3）
- ✅ PCA降维2D可视化
- ✅ LDA主题模型（num_topics=4）
- ✅ 主题关键词提取

### 6. 观点挖掘与社交网络分析
- ✅ 基于依存句法的观点提取
- ✅ 社交网络图构建（基于共同关键词）
- ✅ 网络中心性分析

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

1. **准备数据**：确保 `绿皮书的影评.csv` 文件在项目目录中
2. **运行分析**：打开 `绿皮书影评分析.ipynb` 并按顺序执行所有单元格
3. **查看结果**：分析完成后会生成多个可视化图表和报告文件

## 输出文件

### 数据文件
- `绿皮书影评分析结果.csv` - 处理后的数据集
- `模型性能报告.txt` - 机器学习模型性能报告

### 可视化图表
- `情感分析结果.png` - 情感分布图表
- `高频词分析.png` - 高频词统计图
- `绿皮书影评词云.png` - 词云图
- `特殊关键词分析.png` - 关键词频次分析
- `模型性能比较.png` - 机器学习模型性能对比
- `混淆矩阵热力图.png` - 模型混淆矩阵
- `聚类结果可视化.png` - K-means聚类结果
- `LDA主题分布.png` - 主题模型分布
- `社交网络图.png` - 用户社交网络图

## 技术栈

- **Python 3.8+**
- **数据处理**: pandas, numpy
- **文本处理**: jieba, snownlp
- **机器学习**: scikit-learn
- **可视化**: matplotlib, seaborn
- **词云**: wordcloud
- **主题模型**: gensim
- **网络分析**: networkx

## 项目结构

```
├── 绿皮书的影评.csv          # 原始数据文件
├── 绿皮书影评分析.ipynb      # 主要分析notebook
├── requirements.txt          # 依赖包列表
├── README.md                # 项目说明文档
└── 输出文件/                # 分析结果文件夹
    ├── *.png               # 可视化图表
    ├── *.csv               # 处理后数据
    └── *.txt               # 分析报告
```

## 分析流程

1. **数据加载与预处理** → 清洗文本、分词、去停用词
2. **情感分析** → SnowNLP情感评分、情感分类
3. **词频分析** → TF-IDF分析、词云生成
4. **机器学习建模** → 三种分类器训练与评估
5. **聚类分析** → K-means聚类、PCA可视化
6. **主题模型** → LDA主题提取
7. **观点挖掘** → 观点提取、社交网络构建
8. **结果汇总** → 生成分析报告

## 注意事项

- 确保数据文件编码为UTF-8
- 如果遇到中文字体问题，请安装相应的中文字体
- 某些可选功能（如pyLDAvis）如果安装失败不影响主要分析
- 建议在Python 3.8+环境中运行

## 作者

本项目由AI助手开发，用于演示完整的中文文本分析流程。
