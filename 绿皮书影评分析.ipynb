{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 豆瓣《绿皮书》电影评论多模态文本分析\n", "\n", "## 项目概述\n", "本项目对豆瓣《绿皮书》电影评论进行全面的文本分析，包括：\n", "- 数据预处理与清洗\n", "- 情感倾向分析\n", "- 高频词与词云分析\n", "- 机器学习预测建模\n", "- 文本聚类与LDA主题模型\n", "- 观点挖掘与社交网络分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 导入必要的库\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import jieba\n", "import jieba.posseg as pseg\n", "import re\n", "import warnings\n", "from collections import Counter\n", "from wordcloud import WordCloud\n", "import matplotlib.font_manager as fm\n", "\n", "# 机器学习相关\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.tree import DecisionTreeClassifier\n", "from sklearn.naive_bayes import MultinomialNB\n", "from sklearn.svm import SVC\n", "from sklearn.metrics import accuracy_score, f1_score, confusion_matrix, classification_report\n", "from sklearn.cluster import KMeans\n", "from sklearn.decomposition import PCA\n", "\n", "# 情感分析\n", "from snownlp import SnowNLP\n", "\n", "# LDA主题模型\n", "from gensim import corpora, models\n", "import pyLDAvis.gensim_models as pyLDAvis\n", "\n", "# 网络分析\n", "import networkx as nx\n", "from itertools import combinations\n", "import hashlib\n", "\n", "# 设置中文字体和样式\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "sns.set_style('whitegrid')\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"所有库导入成功！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 数据加载与预处理"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 读取数据\n", "try:\n", "    df = pd.read_csv('绿皮书的影评.csv', encoding='utf-8')\n", "except UnicodeDecodeError:\n", "    df = pd.read_csv('绿皮书的影评.csv', encoding='gbk')\n", "\n", "print(f\"数据集大小: {df.shape}\")\n", "print(f\"列名: {df.columns.tolist()}\")\n", "print(\"\\n前5行数据:\")\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 数据清洗和预处理\n", "# 删除空值\n", "df = df.dropna(subset=['评论'])\n", "\n", "# 重置索引\n", "df = df.reset_index(drop=True)\n", "\n", "print(f\"清洗后数据集大小: {df.shape}\")\n", "print(f\"评论字段统计信息:\")\n", "df['评论'].describe()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 自定义词典 - 添加电影相关专有名词\n", "custom_words = [\n", "    '绿皮书', '种族歧视', '钢琴家', '司机', '友谊', '温暖', '治愈',\n", "    '人生选择', '奥斯卡', '最佳影片', '维果·莫滕森', '马赫沙拉·阿里',\n", "    '真实故事', '南方巡演', '黑人音乐家', '白人司机', '跨种族友谊',\n", "    '社会偏见', '人性光辉', '感人至深', '催泪', '正能量'\n", "]\n", "\n", "# 添加自定义词典\n", "for word in custom_words:\n", "    jieba.add_word(word)\n", "\n", "print(\"自定义词典添加完成！\")\n", "print(f\"添加了 {len(custom_words)} 个专有名词\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 中文停用词\n", "stopwords = {\n", "    '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这', '那', '里', '后', '以', '时', '来', '用', '们', '生', '大', '出', '对', '于', '起', '还', '发', '下', '过', '小', '么', '心', '多', '天', '而', '能', '作', '只', '想', '开', '通', '年', '走', '十', '从', '他', '无', '由', '前', '明', '行', '方', '又', '如', '高', '手', '知', '理', '眼', '志', '点', '心', '战', '二', '问', '但', '身', '方', '实', '吃', '做', '叫', '当', '住', '听', '革', '打', '呢', '真', '全', '才', '四', '已', '所', '敌', '之', '最', '光', '产', '情', '路', '分', '总', '条', '白', '话', '东', '席', '次', '亲', '如', '被', '花', '口', '放', '儿', '常', '气', '五', '第', '使', '写', '军', '吗', '文', '运', '再', '果', '怎', '定', '许', '快', '明', '行', '因', '别', '飞', '外', '树', '物', '活', '部', '门', '无', '往', '船', '望', '新', '带', '队', '先', '力', '完', '却', '足', '七', '数', '石', '满', '决', '百', '原', '拿', '群', '究', '各', '六', '本', '思', '解', '立', '河', '村', '八', '难', '早', '论', '吗', '根', '共', '让', '相', '研', '今', '其', '书', '坐', '接', '应', '关', '信', '觉', '步', '反', '处', '记', '将', '千', '找', '争', '领', '或', '师', '结', '块', '跑', '谁', '草', '越', '字', '加', '脚', '紧', '爱', '等', '习', '阵', '怕', '月', '青', '半', '火', '法', '题', '建', '赶', '位', '唱', '海', '七', '女', '任', '件', '感', '准', '张', '团', '屋', '离', '色', '脸', '片', '科', '倒', '睛', '利', '世', '刚', '且', '由', '送', '切', '星', '导', '晚', '表', '够', '整', '认', '响', '雪', '流', '未', '场', '该', '并', '底', '深', '刻', '平', '伟', '忙', '太', '获', '承', '至', '死', '级', '第', '象', '命', '它', '变', '社', '似', '士', '者', '干', '石', '满', '日', '决', '百', '原', '拿', '群', '究', '各', '六', '本', '思', '解', '立', '河', '村', '八', '难', '早', '论', '吗', '根', '共', '让', '相', '研', '今', '其', '书', '坐', '接', '应', '关', '信', '觉', '步', '反', '处', '记', '将', '千', '找', '争', '领', '或', '师', '结', '块', '跑', '谁', '草', '越', '字', '加', '脚', '紧', '爱', '等', '习', '阵', '怕', '月', '青', '半', '火', '法', '题', '建', '赶', '位', '唱', '海', '七', '女', '任', '件', '感', '准', '张', '团', '屋', '离', '色', '脸', '片', '科', '倒', '睛', '利', '世', '刚', '且', '由', '送', '切', '星', '导', '晚', '表', '够', '整', '认', '响', '雪', '流', '未', '场', '该', '并', '底', '深', '刻', '平', '伟', '忙', '太', '获', '承', '至', '死', '级', '第', '象', '命', '它', '变', '社', '似', '士', '者', '干'\n", "}\n", "\n", "print(f\"停用词库大小: {len(stopwords)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 文本清洗函数\n", "def clean_text(text):\n", "    \"\"\"清洗文本：去除标点、特殊符号、数字等\"\"\"\n", "    if pd.isna(text):\n", "        return ''\n", "    \n", "    # 转换为字符串\n", "    text = str(text)\n", "    \n", "    # 去除HTML标签\n", "    text = re.sub(r'<[^>]+>', '', text)\n", "    \n", "    # 去除URL\n", "    text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)\n", "    \n", "    # 去除邮箱\n", "    text = re.sub(r'\\S+@\\S+', '', text)\n", "    \n", "    # 保留中文、英文、数字\n", "    text = re.sub(r'[^\\u4e00-\\u9fa5a-zA-Z0-9\\s]', '', text)\n", "    \n", "    # 去除多余空格\n", "    text = re.sub(r'\\s+', ' ', text).strip()\n", "    \n", "    return text\n", "\n", "# 分词函数\n", "def tokenize_text(text):\n", "    \"\"\"分词并去除停用词\"\"\"\n", "    if not text:\n", "        return []\n", "    \n", "    # 使用jieba精确模式分词\n", "    words = jieba.lcut(text, cut_all=False)\n", "    \n", "    # 过滤停用词和长度小于2的词\n", "    filtered_words = [word for word in words if word not in stopwords and len(word) >= 2]\n", "    \n", "    return filtered_words\n", "\n", "print(\"文本处理函数定义完成！\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 应用文本清洗\n", "print(\"开始文本清洗...\")\n", "df['cleaned_text'] = df['评论'].apply(clean_text)\n", "\n", "# 分词\n", "print(\"开始分词...\")\n", "df['tokens'] = df['cleaned_text'].apply(tokenize_text)\n", "\n", "# 创建分词后的文本字符串\n", "df['tokenized_text'] = df['tokens'].apply(lambda x: ' '.join(x))\n", "\n", "# 过滤掉空文本\n", "df = df[df['tokenized_text'].str.len() > 0]\n", "df = df.reset_index(drop=True)\n", "\n", "print(f\"处理后的数据集大小: {df.shape}\")\n", "print(\"\\n示例处理结果:\")\n", "for i in range(3):\n", "    print(f\"原文: {df['评论'].iloc[i][:100]}...\")\n", "    print(f\"清洗后: {df['cleaned_text'].iloc[i][:100]}...\")\n", "    print(f\"分词后: {df['tokenized_text'].iloc[i][:100]}...\")\n", "    print(\"-\" * 50)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 情感倾向分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 使用SnowNLP进行情感分析\n", "def get_sentiment_score(text):\n", "    \"\"\"获取情感评分 (0-1, 越接近1越积极)\"\"\"\n", "    try:\n", "        s = SnowNLP(text)\n", "        return s.sentiments\n", "    except:\n", "        return 0.5  # 中性\n", "\n", "print(\"开始情感分析...\")\n", "df['sentiment_score'] = df['cleaned_text'].apply(get_sentiment_score)\n", "\n", "# 根据情感评分分类\n", "def classify_sentiment(score):\n", "    if score >= 0.6:\n", "        return '积极'\n", "    elif score <= 0.4:\n", "        return '消极'\n", "    else:\n", "        return '中性'\n", "\n", "df['sentiment_label'] = df['sentiment_score'].apply(classify_sentiment)\n", "\n", "print(\"情感分析完成！\")\n", "print(f\"情感分布统计:\")\n", "print(df['sentiment_label'].value_counts())\n", "print(f\"\\n平均情感评分: {df['sentiment_score'].mean():.3f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 情感分布可视化\n", "fig, axes = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# 情感分布直方图\n", "axes[0].hist(df['sentiment_score'], bins=30, alpha=0.7, color='skyblue', edgecolor='black')\n", "axes[0].set_xlabel('情感评分')\n", "axes[0].set_ylabel('频次')\n", "axes[0].set_title('情感评分分布直方图')\n", "axes[0].axvline(df['sentiment_score'].mean(), color='red', linestyle='--', label=f'平均值: {df['sentiment_score'].mean():.3f}')\n", "axes[0].legend()\n", "\n", "# 情感分类饼图\n", "sentiment_counts = df['sentiment_label'].value_counts()\n", "colors = ['#ff9999', '#66b3ff', '#99ff99']\n", "axes[1].pie(sentiment_counts.values, labels=sentiment_counts.index, autopct='%1.1f%%', \n", "           colors=colors, startangle=90)\n", "axes[1].set_title('情感分类占比')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 保存图片\n", "plt.savefig('情感分析结果.png', dpi=300, bbox_inches='tight')\n", "print(\"情感分析图表已保存为 '情感分析结果.png'\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 高频词与词云分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# TF-IDF分析\n", "print(\"开始TF-IDF分析...\")\n", "\n", "# 创建TF-IDF向量化器\n", "tfidf_vectorizer = TfidfVectorizer(\n", "    max_features=2000,\n", "    ngram_range=(1, 2),  # 包含单词和双词组合\n", "    min_df=2,  # 至少出现2次\n", "    max_df=0.8  # 最多出现在80%的文档中\n", ")\n", "\n", "# 拟合并转换文本\n", "tfidf_matrix = tfidf_vectorizer.fit_transform(df['tokenized_text'])\n", "feature_names = tfidf_vectorizer.get_feature_names_out()\n", "\n", "# 计算每个词的平均TF-IDF分数\n", "mean_scores = np.mean(tfidf_matrix.toarray(), axis=0)\n", "tfidf_scores = list(zip(feature_names, mean_scores))\n", "tfidf_scores.sort(key=lambda x: x[1], reverse=True)\n", "\n", "print(f\"TF-IDF分析完成！共提取 {len(feature_names)} 个特征\")\n", "print(\"\\nTop 20 TF-IDF词汇:\")\n", "for word, score in tfidf_scores[:20]:\n", "    print(f\"{word}: {score:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 词频统计\n", "all_words = []\n", "for tokens in df['tokens']:\n", "    all_words.extend(tokens)\n", "\n", "word_freq = Counter(all_words)\n", "top_50_words = word_freq.most_common(50)\n", "\n", "print(\"Top 50 高频词:\")\n", "for word, freq in top_50_words:\n", "    print(f\"{word}: {freq}\")\n", "\n", "# 可视化高频词\n", "plt.figure(figsize=(15, 8))\n", "words, freqs = zip(*top_50_words[:20])\n", "plt.barh(range(len(words)), freqs)\n", "plt.yticks(range(len(words)), words)\n", "plt.xlabel('词频')\n", "plt.title('Top 20 高频词分布')\n", "plt.gca().invert_yaxis()\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 保存图片\n", "plt.savefig('高频词分析.png', dpi=300, bbox_inches='tight')\n", "print(\"高频词分析图表已保存为 '高频词分析.png'\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 生成词云\n", "print(\"开始生成词云...\")\n", "\n", "# 合并所有分词文本\n", "all_text = ' '.join(df['tokenized_text'])\n", "\n", "# 创建词云对象\n", "wordcloud = WordCloud(\n", "    width=1200,\n", "    height=800,\n", "    background_color='white',\n", "    font_path='simhei.ttf',  # 中文字体路径，如果没有可以注释掉\n", "    max_words=200,\n", "    relative_scaling=0.5,\n", "    colormap='viridis'\n", ").generate(all_text)\n", "\n", "# 显示词云\n", "plt.figure(figsize=(15, 10))\n", "plt.imshow(wordcloud, interpolation='bilinear')\n", "plt.axis('off')\n", "plt.title('《绿皮书》影评词云图', fontsize=20, pad=20)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 保存词云图\n", "wordcloud.to_file('绿皮书影评词云.png')\n", "print(\"词云图已保存为 '绿皮书影评词云.png'\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 特殊关键词分析\n", "special_keywords = ['温暖', '治愈', '人生选择', '友谊', '种族', '歧视', '感动', '催泪', '真实', '奥斯卡']\n", "\n", "keyword_counts = {}\n", "for keyword in special_keywords:\n", "    count = sum(1 for tokens in df['tokens'] if keyword in tokens)\n", "    keyword_counts[keyword] = count\n", "\n", "print(\"特殊关键词出现频次:\")\n", "for keyword, count in sorted(keyword_counts.items(), key=lambda x: x[1], reverse=True):\n", "    print(f\"{keyword}: {count} 次\")\n", "\n", "# 可视化特殊关键词\n", "plt.figure(figsize=(12, 6))\n", "keywords, counts = zip(*sorted(keyword_counts.items(), key=lambda x: x[1], reverse=True))\n", "plt.bar(keywords, counts, color='lightcoral', alpha=0.7)\n", "plt.xlabel('关键词')\n", "plt.ylabel('出现次数')\n", "plt.title('特殊关键词频次分析')\n", "plt.xticks(rotation=45)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 保存图片\n", "plt.savefig('特殊关键词分析.png', dpi=300, bbox_inches='tight')\n", "print(\"特殊关键词分析图表已保存为 '特殊关键词分析.png'\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 机器学习预测建模"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 准备机器学习数据\n", "print(\"准备机器学习数据...\")\n", "\n", "# 使用TF-IDF特征\n", "X = tfidf_matrix.toarray()\n", "y = df['sentiment_label']\n", "\n", "# 划分训练集和测试集\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.3, random_state=42, stratify=y\n", ")\n", "\n", "print(f\"训练集大小: {X_train.shape}\")\n", "print(f\"测试集大小: {X_test.shape}\")\n", "print(f\"特征维度: {X.shape[1]}\")\n", "\n", "# 标签分布\n", "print(\"\\n训练集标签分布:\")\n", "print(pd.Series(y_train).value_counts())\n", "print(\"\\n测试集标签分布:\")\n", "print(pd.Series(y_test).value_counts())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 模型1: 决策树分类器\n", "print(\"训练决策树分类器...\")\n", "\n", "dt_classifier = DecisionTreeClassifier(\n", "    max_depth=5,\n", "    criterion='entropy',\n", "    random_state=42,\n", "    min_samples_split=10,\n", "    min_samples_leaf=5\n", ")\n", "\n", "dt_classifier.fit(X_train, y_train)\n", "dt_pred = dt_classifier.predict(X_test)\n", "\n", "# 评估决策树\n", "dt_accuracy = accuracy_score(y_test, dt_pred)\n", "dt_f1 = f1_score(y_test, dt_pred, average='weighted')\n", "\n", "print(f\"决策树准确率: {dt_accuracy:.4f}\")\n", "print(f\"决策树F1分数: {dt_f1:.4f}\")\n", "print(\"\\n决策树分类报告:\")\n", "print(classification_report(y_test, dt_pred))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 模型2: 朴素贝叶斯分类器\n", "print(\"训练朴素贝叶斯分类器...\")\n", "\n", "nb_classifier = MultinomialNB(alpha=1.0)\n", "nb_classifier.fit(X_train, y_train)\n", "nb_pred = nb_classifier.predict(X_test)\n", "\n", "# 评估朴素贝叶斯\n", "nb_accuracy = accuracy_score(y_test, nb_pred)\n", "nb_f1 = f1_score(y_test, nb_pred, average='weighted')\n", "\n", "print(f\"朴素贝叶斯准确率: {nb_accuracy:.4f}\")\n", "print(f\"朴素贝叶斯F1分数: {nb_f1:.4f}\")\n", "print(\"\\n朴素贝叶斯分类报告:\")\n", "print(classification_report(y_test, nb_pred))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 模型3: 支持向量机\n", "print(\"训练支持向量机...\")\n", "\n", "svm_classifier = SVC(\n", "    kernel='linear',\n", "    C=1.0,\n", "    random_state=42\n", ")\n", "\n", "svm_classifier.fit(X_train, y_train)\n", "svm_pred = svm_classifier.predict(X_test)\n", "\n", "# 评估SVM\n", "svm_accuracy = accuracy_score(y_test, svm_pred)\n", "svm_f1 = f1_score(y_test, svm_pred, average='weighted')\n", "\n", "print(f\"SVM准确率: {svm_accuracy:.4f}\")\n", "print(f\"SVM F1分数: {svm_f1:.4f}\")\n", "print(\"\\nSVM分类报告:\")\n", "print(classification_report(y_test, svm_pred))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 模型性能比较\n", "models_performance = {\n", "    '决策树': {'accuracy': dt_accuracy, 'f1_score': dt_f1},\n", "    '朴素贝叶斯': {'accuracy': nb_accuracy, 'f1_score': nb_f1},\n", "    'SVM': {'accuracy': svm_accuracy, 'f1_score': svm_f1}\n", "}\n", "\n", "# 可视化模型性能\n", "fig, axes = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# 准确率比较\n", "models = list(models_performance.keys())\n", "accuracies = [models_performance[model]['accuracy'] for model in models]\n", "f1_scores = [models_performance[model]['f1_score'] for model in models]\n", "\n", "axes[0].bar(models, accuracies, color=['skyblue', 'lightgreen', 'lightcoral'], alpha=0.7)\n", "axes[0].set_ylabel('准确率')\n", "axes[0].set_title('模型准确率比较')\n", "axes[0].set_ylim(0, 1)\n", "for i, acc in enumerate(accuracies):\n", "    axes[0].text(i, acc + 0.01, f'{acc:.3f}', ha='center')\n", "\n", "# F1分数比较\n", "axes[1].bar(models, f1_scores, color=['skyblue', 'lightgreen', 'lightcoral'], alpha=0.7)\n", "axes[1].set_ylabel('F1分数')\n", "axes[1].set_title('模型F1分数比较')\n", "axes[1].set_ylim(0, 1)\n", "for i, f1 in enumerate(f1_scores):\n", "    axes[1].text(i, f1 + 0.01, f'{f1:.3f}', ha='center')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 保存图片\n", "plt.savefig('模型性能比较.png', dpi=300, bbox_inches='tight')\n", "print(\"模型性能比较图表已保存为 '模型性能比较.png'\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 混淆矩阵热力图\n", "fig, axes = plt.subplots(1, 3, figsize=(18, 5))\n", "\n", "# 决策树混淆矩阵\n", "dt_cm = confusion_matrix(y_test, dt_pred)\n", "sns.heatmap(dt_cm, annot=True, fmt='d', cmap='Blues', \n", "           xticklabels=['消极', '中性', '积极'], \n", "           yticklabels=['消极', '中性', '积极'], ax=axes[0])\n", "axes[0].set_title('决策树混淆矩阵')\n", "axes[0].set_xlabel('预测标签')\n", "axes[0].set_ylabel('真实标签')\n", "\n", "# 朴素贝叶斯混淆矩阵\n", "nb_cm = confusion_matrix(y_test, nb_pred)\n", "sns.heatmap(nb_cm, annot=True, fmt='d', cmap='Greens',\n", "           xticklabels=['消极', '中性', '积极'], \n", "           yticklabels=['消极', '中性', '积极'], ax=axes[1])\n", "axes[1].set_title('朴素贝叶斯混淆矩阵')\n", "axes[1].set_xlabel('预测标签')\n", "axes[1].set_ylabel('真实标签')\n", "\n", "# SVM混淆矩阵\n", "svm_cm = confusion_matrix(y_test, svm_pred)\n", "sns.heatmap(svm_cm, annot=True, fmt='d', cmap='Reds',\n", "           xticklabels=['消极', '中性', '积极'], \n", "           yticklabels=['消极', '中性', '积极'], ax=axes[2])\n", "axes[2].set_title('SVM混淆矩阵')\n", "axes[2].set_xlabel('预测标签')\n", "axes[2].set_ylabel('真实标签')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 保存图片\n", "plt.savefig('混淆矩阵热力图.png', dpi=300, bbox_inches='tight')\n", "print(\"混淆矩阵热力图已保存为 '混淆矩阵热力图.png'\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 文本聚类与LDA主题模型"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# K-means聚类\n", "print(\"开始K-means聚类分析...\")\n", "\n", "# 使用K-means进行聚类\n", "n_clusters = 3\n", "kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)\n", "cluster_labels = kmeans.fit_predict(X)\n", "\n", "# 添加聚类标签到数据框\n", "df['cluster'] = cluster_labels\n", "\n", "print(f\"聚类完成！共分为 {n_clusters} 个簇\")\n", "print(\"\\n各簇样本数量:\")\n", "cluster_counts = pd.Series(cluster_labels).value_counts().sort_index()\n", "for cluster_id, count in cluster_counts.items():\n", "    print(f\"簇 {cluster_id}: {count} 个样本\")\n", "\n", "# 分析每个簇的情感分布\n", "print(\"\\n各簇情感分布:\")\n", "for cluster_id in range(n_clusters):\n", "    cluster_data = df[df['cluster'] == cluster_id]\n", "    sentiment_dist = cluster_data['sentiment_label'].value_counts()\n", "    print(f\"\\n簇 {cluster_id}:\")\n", "    for sentiment, count in sentiment_dist.items():\n", "        print(f\"  {sentiment}: {count} ({count/len(cluster_data)*100:.1f}%)\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# PCA降维可视化聚类结果\n", "print(\"进行PCA降维可视化...\")\n", "\n", "# PCA降维到2D\n", "pca = PCA(n_components=2, random_state=42)\n", "X_pca = pca.fit_transform(X)\n", "\n", "# 可视化聚类结果\n", "plt.figure(figsize=(12, 8))\n", "colors = ['red', 'blue', 'green', 'purple', 'orange']\n", "for cluster_id in range(n_clusters):\n", "    cluster_points = X_pca[cluster_labels == cluster_id]\n", "    plt.scatter(cluster_points[:, 0], cluster_points[:, 1], \n", "               c=colors[cluster_id], label=f'簇 {cluster_id}', alpha=0.6)\n", "\n", "# 绘制聚类中心\n", "centers_pca = pca.transform(kmeans.cluster_centers_)\n", "plt.scatter(centers_pca[:, 0], centers_pca[:, 1], \n", "           c='black', marker='x', s=200, linewidths=3, label='聚类中心')\n", "\n", "plt.xlabel(f'第一主成分 (解释方差: {pca.explained_variance_ratio_[0]:.3f})')\n", "plt.ylabel(f'第二主成分 (解释方差: {pca.explained_variance_ratio_[1]:.3f})')\n", "plt.title('K-means聚类结果 (PCA降维可视化)')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 保存图片\n", "plt.savefig('聚类结果可视化.png', dpi=300, bbox_inches='tight')\n", "print(\"聚类结果可视化图已保存为 '聚类结果可视化.png'\")\n", "\n", "print(f\"\\nPCA解释的总方差: {sum(pca.explained_variance_ratio_):.3f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# LDA主题模型\n", "print(\"开始LDA主题模型分析...\")\n", "\n", "# 准备LDA数据\n", "texts = [tokens for tokens in df['tokens'] if len(tokens) > 0]\n", "\n", "# 创建词典和语料库\n", "dictionary = corpora.Dictionary(texts)\n", "dictionary.filter_extremes(no_below=2, no_above=0.8)  # 过滤极端词汇\n", "corpus = [dictionary.doc2bow(text) for text in texts]\n", "\n", "print(f\"词典大小: {len(dictionary)}\")\n", "print(f\"语料库大小: {len(corpus)}\")\n", "\n", "# 训练LDA模型\n", "num_topics = 4\n", "lda_model = models.LdaModel(\n", "    corpus=corpus,\n", "    id2word=dictionary,\n", "    num_topics=num_topics,\n", "    random_state=42,\n", "    passes=10,\n", "    alpha='auto',\n", "    per_word_topics=True\n", ")\n", "\n", "print(f\"\\nLDA模型训练完成！共提取 {num_topics} 个主题\")\n", "\n", "# 打印每个主题的关键词\n", "print(\"\\n各主题的Top 10关键词:\")\n", "for topic_id in range(num_topics):\n", "    topic_words = lda_model.show_topic(topic_id, topn=10)\n", "    print(f\"\\n主题 {topic_id + 1}:\")\n", "    for word, prob in topic_words:\n", "        print(f\"  {word}: {prob:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 可视化主题分布\n", "try:\n", "    # 尝试使用pyLDAvis进行交互式可视化\n", "    import pyLDAvis\n", "    import pyLDAvis.gensim_models as gensimvis\n", "    \n", "    # 准备可视化数据\n", "    vis_data = gensimvis.prepare(lda_model, corpus, dictionary)\n", "    \n", "    # 保存为HTML文件\n", "    pyLDAvis.save_html(vis_data, 'LDA主题模型可视化.html')\n", "    print(\"LDA主题模型交互式可视化已保存为 'LDA主题模型可视化.html'\")\n", "    \n", "except ImportError:\n", "    print(\"pyLDAvis未安装，跳过交互式可视化\")\n", "\n", "# 简单的主题分布可视化\n", "topic_distributions = []\n", "for doc in corpus:\n", "    doc_topics = lda_model.get_document_topics(doc)\n", "    topic_dist = [0] * num_topics\n", "    for topic_id, prob in doc_topics:\n", "        topic_dist[topic_id] = prob\n", "    topic_distributions.append(topic_dist)\n", "\n", "topic_distributions = np.array(topic_distributions)\n", "\n", "# 绘制主题分布\n", "plt.figure(figsize=(12, 6))\n", "for i in range(num_topics):\n", "    plt.hist(topic_distributions[:, i], bins=20, alpha=0.7, label=f'主题 {i+1}')\n", "\n", "plt.xlabel('主题概率')\n", "plt.ylabel('文档数量')\n", "plt.title('LDA主题分布')\n", "plt.legend()\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 保存图片\n", "plt.savefig('LDA主题分布.png', dpi=300, bbox_inches='tight')\n", "print(\"LDA主题分布图已保存为 'LDA主题分布.png'\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 观点挖掘与社交网络分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 观点提取（基于依存句法分析的简化版本）\n", "print(\"开始观点挖掘...\")\n", "\n", "# 定义评价词汇\n", "positive_words = {'好', '棒', '优秀', '精彩', '感动', '温暖', '治愈', '完美', '杰出', '震撼', '深刻', '真实', '动人', '催泪', '美好', '经典'}\n", "negative_words = {'差', '烂', '无聊', '失望', '糟糕', '平庸', '乏味', '虚假', '做作', '拖沓', '老套', '俗套'}\n", "\n", "# 定义主题词汇\n", "subject_words = {'电影', '影片', '故事', '情节', '剧情', '演技', '表演', '导演', '音乐', '画面', '台词', '角色', '人物', '主题', '友谊', '种族', '歧视'}\n", "\n", "def extract_opinions(tokens):\n", "    \"\"\"提取观点：主语+评价词组合\"\"\"\n", "    opinions = []\n", "    \n", "    for i, token in enumerate(tokens):\n", "        if token in subject_words:\n", "            # 查找附近的评价词\n", "            for j in range(max(0, i-3), min(len(tokens), i+4)):\n", "                if tokens[j] in positive_words:\n", "                    opinions.append((token, tokens[j], '积极'))\n", "                elif tokens[j] in negative_words:\n", "                    opinions.append((token, tokens[j], '消极'))\n", "    \n", "    return opinions\n", "\n", "# 提取所有观点\n", "all_opinions = []\n", "for tokens in df['tokens']:\n", "    opinions = extract_opinions(tokens)\n", "    all_opinions.extend(opinions)\n", "\n", "print(f\"共提取到 {len(all_opinions)} 个观点\")\n", "\n", "# 统计观点频次\n", "opinion_counter = Counter(all_opinions)\n", "top_opinions = opinion_counter.most_common(20)\n", "\n", "print(\"\\nTop 20 观点:\")\n", "for (subject, predicate, sentiment), count in top_opinions:\n", "    print(f\"{subject} → {predicate} ({sentiment}): {count} 次\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 社交网络分析\n", "print(\"开始社交网络分析...\")\n", "\n", "# 生成用户ID（基于评论时间或用户名的哈希）\n", "def generate_user_id(user_info):\n", "    \"\"\"生成用户ID\"\"\"\n", "    if pd.isna(user_info):\n", "        return hashlib.md5(str(np.random.random()).encode()).hexdigest()[:8]\n", "    return hashlib.md5(str(user_info).encode()).hexdigest()[:8]\n", "\n", "df['user_id'] = df['用户'].apply(generate_user_id)\n", "\n", "# 基于共同关键词构建网络\n", "def build_social_network(df, threshold=3):\n", "    \"\"\"基于共同关键词构建社交网络\"\"\"\n", "    G = nx.Graph()\n", "    \n", "    # 添加节点\n", "    for user_id in df['user_id'].unique():\n", "        G.add_node(user_id)\n", "    \n", "    # 计算用户间的共同关键词\n", "    user_keywords = {}\n", "    for _, row in df.iterrows():\n", "        user_id = row['user_id']\n", "        keywords = set(row['tokens'])\n", "        if user_id not in user_keywords:\n", "            user_keywords[user_id] = set()\n", "        user_keywords[user_id].update(keywords)\n", "    \n", "    # 添加边（基于共同关键词数量）\n", "    users = list(user_keywords.keys())\n", "    for i in range(len(users)):\n", "        for j in range(i+1, len(users)):\n", "            user1, user2 = users[i], users[j]\n", "            common_keywords = user_keywords[user1] & user_keywords[user2]\n", "            if len(common_keywords) >= threshold:\n", "                G.add_edge(user1, user2, weight=len(common_keywords))\n", "    \n", "    return G\n", "\n", "# 构建网络（使用较小的阈值以确保有连接）\n", "G = build_social_network(df, threshold=2)\n", "\n", "print(f\"网络节点数: {G.number_of_nodes()}\")\n", "print(f\"网络边数: {G.number_of_edges()}\")\n", "print(f\"网络密度: {nx.density(G):.4f}\")\n", "\n", "if G.number_of_edges() > 0:\n", "    # 计算网络指标\n", "    degree_centrality = nx.degree_centrality(G)\n", "    betweenness_centrality = nx.betweenness_centrality(G)\n", "    \n", "    print(f\"\\n平均度中心性: {np.mean(list(degree_centrality.values())):.4f}\")\n", "    print(f\"平均介数中心性: {np.mean(list(betweenness_centrality.values())):.4f}\")\n", "else:\n", "    print(\"网络中没有边，无法计算中心性指标\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 可视化社交网络\n", "if G.number_of_edges() > 0:\n", "    plt.figure(figsize=(15, 12))\n", "    \n", "    # 选择最大连通分量进行可视化\n", "    largest_cc = max(nx.connected_components(G), key=len)\n", "    G_largest = G.subgraph(largest_cc)\n", "    \n", "    print(f\"最大连通分量包含 {len(largest_cc)} 个节点\")\n", "    \n", "    # 使用spring布局\n", "    pos = nx.spring_layout(G_largest, k=1, iterations=50)\n", "    \n", "    # 绘制网络\n", "    nx.draw_networkx_nodes(G_largest, pos, \n", "                          node_color='lightblue', \n", "                          node_size=300, \n", "                          alpha=0.7)\n", "    \n", "    nx.draw_networkx_edges(G_largest, pos, \n", "                          alpha=0.5, \n", "                          width=0.5)\n", "    \n", "    plt.title('用户社交网络图\\n(基于共同关键词连接)', fontsize=16)\n", "    plt.axis('off')\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # 保存图片\n", "    plt.savefig('社交网络图.png', dpi=300, bbox_inches='tight')\n", "    print(\"社交网络图已保存为 '社交网络图.png'\")\n", "    \n", "else:\n", "    print(\"网络中没有边，无法绘制网络图\")\n", "    \n", "    # 创建一个简单的节点分布图\n", "    plt.figure(figsize=(10, 8))\n", "    plt.scatter(range(len(df)), [1]*len(df), alpha=0.6, s=50)\n", "    plt.title('用户分布图（无连接）')\n", "    plt.xlabel('用户索引')\n", "    plt.ylabel('用户')\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    plt.savefig('用户分布图.png', dpi=300, bbox_inches='tight')\n", "    print(\"用户分布图已保存为 '用户分布图.png'\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 分析总结与结论"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 生成分析报告\n", "print(\"=\" * 60)\n", "print(\"豆瓣《绿皮书》电影评论多模态文本分析报告\")\n", "print(\"=\" * 60)\n", "\n", "print(f\"\\n📊 数据概览:\")\n", "print(f\"  • 总评论数: {len(df)}\")\n", "print(f\"  • 平均评论长度: {df['cleaned_text'].str.len().mean():.1f} 字符\")\n", "print(f\"  • 独特用户数: {df['user_id'].nunique()}\")\n", "\n", "print(f\"\\n😊 情感分析结果:\")\n", "sentiment_stats = df['sentiment_label'].value_counts()\n", "for sentiment, count in sentiment_stats.items():\n", "    percentage = count / len(df) * 100\n", "    print(f\"  • {sentiment}: {count} 条 ({percentage:.1f}%)\")\n", "print(f\"  • 平均情感评分: {df['sentiment_score'].mean():.3f} (0-1区间)\")\n", "\n", "print(f\"\\n🔤 词汇分析:\")\n", "print(f\"  • TF-IDF特征数: {len(feature_names)}\")\n", "print(f\"  • 总词汇量: {len(word_freq)}\")\n", "print(f\"  • 最高频词: {top_50_words[0][0]} (出现{top_50_words[0][1]}次)\")\n", "\n", "print(f\"\\n🤖 机器学习模型性能:\")\n", "for model_name, performance in models_performance.items():\n", "    print(f\"  • {model_name}: 准确率 {performance['accuracy']:.3f}, F1分数 {performance['f1_score']:.3f}\")\n", "\n", "print(f\"\\n🎯 聚类分析:\")\n", "print(f\"  • 聚类数: {n_clusters}\")\n", "for cluster_id in range(n_clusters):\n", "    cluster_size = sum(cluster_labels == cluster_id)\n", "    print(f\"  • 簇 {cluster_id}: {cluster_size} 个样本\")\n", "\n", "print(f\"\\n📚 主题模型:\")\n", "print(f\"  • 主题数: {num_topics}\")\n", "print(f\"  • 词典大小: {len(dictionary)}\")\n", "print(f\"  • 语料库大小: {len(corpus)}\")\n", "\n", "print(f\"\\n💭 观点挖掘:\")\n", "print(f\"  • 提取观点数: {len(all_opinions)}\")\n", "if top_opinions:\n", "    top_opinion = top_opinions[0]\n", "    print(f\"  • 最频繁观点: {top_opinion[0][0]} → {top_opinion[0][1]} ({top_opinion[1]}次)\")\n", "\n", "print(f\"\\n🌐 社交网络:\")\n", "print(f\"  • 网络节点数: {G.number_of_nodes()}\")\n", "print(f\"  • 网络边数: {G.number_of_edges()}\")\n", "print(f\"  • 网络密度: {nx.density(G):.4f}\")\n", "\n", "print(\"\\n\" + \"=\" * 60)\n", "print(\"分析完成！所有图表和数据已保存到当前目录。\")\n", "print(\"=\" * 60)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# 保存处理后的数据\n", "output_df = df[['用户', '评论', '点赞', '评论数', 'cleaned_text', 'sentiment_score', \n", "               'sentiment_label', 'cluster', 'user_id']].copy()\n", "\n", "output_df.to_csv('绿皮书影评分析结果.csv', index=False, encoding='utf-8-sig')\n", "print(\"分析结果已保存为 '绿皮书影评分析结果.csv'\")\n", "\n", "# 保存模型性能报告\n", "with open('模型性能报告.txt', 'w', encoding='utf-8') as f:\n", "    f.write(\"豆瓣《绿皮书》电影评论分析 - 模型性能报告\\n\")\n", "    f.write(\"=\" * 50 + \"\\n\\n\")\n", "    \n", "    f.write(\"1. 数据集信息:\\n\")\n", "    f.write(f\"   总样本数: {len(df)}\\n\")\n", "    f.write(f\"   特征维度: {X.shape[1]}\\n\")\n", "    f.write(f\"   训练集大小: {X_train.shape[0]}\\n\")\n", "    f.write(f\"   测试集大小: {X_test.shape[0]}\\n\\n\")\n", "    \n", "    f.write(\"2. 模型性能对比:\\n\")\n", "    for model_name, performance in models_performance.items():\n", "        f.write(f\"   {model_name}:\\n\")\n", "        f.write(f\"     准确率: {performance['accuracy']:.4f}\\n\")\n", "        f.write(f\"     F1分数: {performance['f1_score']:.4f}\\n\\n\")\n", "    \n", "    f.write(\"3. 情感分布:\\n\")\n", "    for sentiment, count in sentiment_stats.items():\n", "        percentage = count / len(df) * 100\n", "        f.write(f\"   {sentiment}: {count} ({percentage:.1f}%)\\n\")\n", "    \n", "    f.write(f\"\\n4. 平均情感评分: {df['sentiment_score'].mean():.4f}\\n\")\n", "\n", "print(\"模型性能报告已保存为 '模型性能报告.txt'\")\n", "\n", "print(\"\\n🎉 所有分析任务完成！\")\n", "print(\"\\n生成的文件列表:\")\n", "generated_files = [\n", "    '绿皮书影评分析结果.csv',\n", "    '模型性能报告.txt',\n", "    '情感分析结果.png',\n", "    '高频词分析.png',\n", "    '绿皮书影评词云.png',\n", "    '特殊关键词分析.png',\n", "    '模型性能比较.png',\n", "    '混淆矩阵热力图.png',\n", "    '聚类结果可视化.png',\n", "    'LDA主题分布.png',\n", "    '社交网络图.png' if G.number_of_edges() > 0 else '用户分布图.png'\n", "]\n", "\n", "for i, file in enumerate(generated_files, 1):\n", "    print(f\"{i:2d}. {file}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}